import { NextResponse } from "next/server"

// Mock data matching your API structure
const mockGroups = [
  {
    id: "beratungen",
    name: "Berat<PERSON>",
    services: [
      {
        id: "consultation",
        name: "*Ich möchte mich erstmal beraten lassen*",
        externalName: "Analyse und Beratung durch Frau Wolanin",
        duration: 30,
        price: "50.00",
        gross: 59.5,
        groupNames: ["Beratungen"],
        productGroup: "consultation",
      },
      {
        id: "thread-lifting-consultation",
        name: "Fadenlifting (Beratung)",
        externalName: "Wird bei Behandlung verrechnet",
        duration: 30,
        price: "100.00",
        gross: 119.0,
        groupNames: ["Beratungen"],
        productGroup: "consultation",
      },
    ],
  },
  {
    id: "fett-weg-spritze",
    name: "<PERSON><PERSON>-weg-Spritze",
    services: [
      {
        id: "lemon-bottle",
        name: "Lemon Bottle",
        externalName: "Lemon Bottle Treatment",
        duration: 30,
        price: "250.00",
        gross: 297.5,
        groupNames: ["Fett-weg-Spritze"],
        productGroup: "fat-dissolving",
      },
      {
        id: "fat-dissolving",
        name: "<PERSON>tt-weg-<PERSON><PERSON>ritz<PERSON> (z.B. Lemon Bottle etc.)",
        externalName: "Fettauflösung durch Lemon Bottle oder Enzyme",
        duration: 30,
        price: "250.00",
        gross: 297.5,
        groupNames: ["Fett-weg-Spritze"],
        productGroup: "fat-dissolving",
      },
    ],
  },
  {
    id: "koerper",
    name: "Körper",
    services: [
      {
        id: "control-appointment",
        name: "Kontrolltermin",
        externalName: "Follow-up appointment",
        duration: 30,
        price: "50.00",
        gross: 59.5,
        groupNames: ["Körper"],
        productGroup: "body-treatment",
      },
      {
        id: "therapy-continuation",
        name: "Fortsetzung der Therapie",
        externalName: "Folgebehandlung der laufenden Therapie",
        duration: 30,
        price: "0.00",
        gross: 0.0,
        groupNames: ["Körper"],
        productGroup: "body-treatment",
      },
    ],
  },
]

export async function GET() {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  return NextResponse.json({ groups: mockGroups })
}
