import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  const appointmentData = await request.json()

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Mock booking logic
  console.log("Booking appointment:", appointmentData)

  return NextResponse.json({
    success: true,
    appointmentId: "APT-" + Date.now(),
    message: "Appointment booked successfully",
  })
}
