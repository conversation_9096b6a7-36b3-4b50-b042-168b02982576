import { type NextRequest, NextResponse } from "next/server"

interface Appointment {
  id: string
  date: string
  user: number
  service: number
  location: null | string
  resources: number[]
}

type Slots = Record<string, Appointment[]>

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const startDate = searchParams.get("startDate")
  const direction = searchParams.get("direction")

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  const startDateObj = new Date(startDate || new Date().toISOString().split("T")[0])

  // Generate mock appointments in the format you specified
  const generateMockSlots = (fromDate: Date, monthsCount: number): Slots => {
    const slots: Slots = {}

    for (let monthOffset = 0; monthOffset < monthsCount; monthOffset++) {
      const currentMonth = new Date(fromDate.getFullYear(), fromDate.getMonth() + monthOffset, 1)
      const daysInMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate()

      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)

        // Skip past dates
        if (currentDate < new Date()) continue

        const dateString = currentDate.toISOString().split("T")[0]

        // Randomly generate some appointments (to simulate busy slots)
        const appointments: Appointment[] = []

        // Sometimes add appointments to make slots unavailable
        if (Math.random() > 0.7) {
          // 30% chance of having appointments
          const timeSlots = [
            { hour: 10, minute: 0 },
            { hour: 11, minute: 0 },
            { hour: 13, minute: 30 },
            { hour: 16, minute: 0 },
            { hour: 17, minute: 0 },
          ]

          // Randomly pick 1-2 time slots to be busy
          const busySlots = timeSlots.slice(0, Math.floor(Math.random() * 2) + 1)

          busySlots.forEach((slot, index) => {
            const appointmentDate = new Date(currentDate)
            appointmentDate.setHours(slot.hour, slot.minute, 0, 0)

            appointments.push({
              id: `${Math.random().toString(36).substr(2, 9)}-${index}`,
              date: appointmentDate.toISOString(),
              user: 5002,
              service: 14 + index,
              location: null,
              resources: [2],
            })
          })
        }

        // Only include dates that have some availability (not completely booked)
        if (appointments.length < 4) {
          // If less than 4 appointments, there are still available slots
          slots[dateString] = appointments
        }
      }
    }

    return slots
  }

  let slots: Slots = {}

  if (direction === "next") {
    // Generate next 3 months from the reference date
    slots = generateMockSlots(startDateObj, 3)
  } else if (direction === "prev") {
    // Generate previous 3 months from the reference date
    const prevDate = new Date(startDateObj.getFullYear(), startDateObj.getMonth() - 3, 1)
    slots = generateMockSlots(prevDate, 3)
  } else {
    // Initial load - generate 3 months starting from startDate
    slots = generateMockSlots(startDateObj, 3)
  }

  return NextResponse.json(slots)
}
