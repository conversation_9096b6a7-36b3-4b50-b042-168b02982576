export const dynamic = 'force-dynamic';
export const maxDuration = 60;
import { type NextRequest, NextResponse } from 'next/server';
import { ccRequest } from '@/lib/clinicore';

interface Appointment {
  id: string;
  date: string;
  user: number;
  service: number;
  location: null | string;
  resources: number[];
}

type Slots = Record<string, Appointment[]>;

/**
 * Group appointments by date
 */
function groupAppointmentsByDate(
  appointments: Appointment[],
): Record<string, Appointment[]> {
  const groupedAppointments: Record<string, Appointment[]> = {};
  appointments.forEach((appointment) => {
    const date = appointment.date.split('T')[0]; // Extract the date portion
    if (!groupedAppointments[date]) {
      groupedAppointments[date] = [];
    }
    groupedAppointments[date].push(appointment);
  });
  return groupedAppointments;
}

/**
 * GET handler for fetching available slots from CliniCore
 */
export async function GET(req: NextRequest) {
  const queryParams = req.nextUrl.searchParams;

  try {
    const service = queryParams.get('service');
    const duration = queryParams.get('duration');
    const user = queryParams.get('user');
    const start = queryParams.get('start');
    const end = queryParams.get('end');

    const now = new Date();
    const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
    const ninetyDaysFromNow = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);

    const query: any = {
      from: twoHoursFromNow.toISOString(),
      to: ninetyDaysFromNow.toISOString(),
    };

    if (duration) {
      query['duration'] = parseInt(duration.toString()) * 60;
    } else {
      query['duration'] = 1800; // 30 minutes default
    }

    if (service) query['service'] = parseInt(service.toString());

    if (user) query['user'] = parseInt(user.toString());

    if (start) {
      const startDate = new Date(start);
      query['from'] = startDate.toISOString();
      const endDate = new Date(startDate.getTime() + 90 * 24 * 60 * 60 * 1000);
      query['to'] = endDate.toISOString();
    }

    if (end) query['to'] = new Date(end).toISOString();

    const result = await ccRequest('/slots', {
      method: 'GET',
      params: query,
    });

    return NextResponse.json({
      status: 200,
      message: 'OK',
      data: groupAppointmentsByDate((result as any)?.slots ?? []),
    });
  } catch (error) {
    console.error('Error fetching slots:', error);

    return NextResponse.json({
      status: 500,
      message: "Fehler beim Laden der Termine. Bitte versuchen Sie es später noch einmal.",
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}