import { createSlice, createAsyncThunk, type PayloadAction } from "@reduxjs/toolkit"

interface Appointment {
  id: string
  date: string
  user: number
  service: number
  location: null | string
  resources: number[]
}

type Slots = Record<string, Appointment[]>

export type TimeSlot = {
  time: string
  available: boolean
  appointments?: Appointment[]
}

export type DaySlot = {
  date: string
  slots: TimeSlot[]
}

export type MonthSlots = {
  month: number
  year: number
  days: DaySlot[]
}

interface SlotsState {
  months: MonthSlots[]
  loading: boolean
  error: string | null
  currentMonthIndex: number
  hasMoreNext: boolean
  hasMorePrev: boolean
  rawSlots: Slots
}

const initialState: SlotsState = {
  months: [],
  loading: false,
  error: null,
  currentMonthIndex: 0,
  hasMoreNext: true,
  hasMorePrev: false,
  rawSlots: {},
}

// Helper function to generate time slots for a day
const generateTimeSlots = (dateString: string, appointments: Appointment[] = []): TimeSlot[] => {
  const timeSlots = ["10:00", "11:00", "12:00", "16:00", "17:00", "18:00"]

  return timeSlots.map((time) => {
    // Check if there are appointments at this time
    const appointmentsAtTime = appointments.filter((apt) => {
      const aptDate = new Date(apt.date)
      const aptTime = `${aptDate.getHours().toString().padStart(2, "0")}:${aptDate.getMinutes().toString().padStart(2, "0")}`
      return aptTime === time
    })

    return {
      time,
      available: appointmentsAtTime.length === 0, // Available if no appointments
      appointments: appointmentsAtTime,
    }
  })
}

// Helper function to convert slots data to months structure
const convertSlotsToMonths = (slots: Slots): MonthSlots[] => {
  const monthsMap = new Map<string, MonthSlots>()

  Object.entries(slots).forEach(([dateString, appointments]) => {
    const date = new Date(dateString)
    const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`

    if (!monthsMap.has(monthKey)) {
      monthsMap.set(monthKey, {
        month: date.getMonth() + 1,
        year: date.getFullYear(),
        days: [],
      })
    }

    const month = monthsMap.get(monthKey)!
    const timeSlots = generateTimeSlots(dateString, appointments)

    // Only add days that have at least one available slot
    if (timeSlots.some((slot) => slot.available)) {
      month.days.push({
        date: dateString,
        slots: timeSlots,
      })
    }
  })

  // Convert map to array and sort by year/month
  return Array.from(monthsMap.values()).sort((a, b) => {
    if (a.year !== b.year) return a.year - b.year
    return a.month - b.month
  })
}

export const fetchSlots = createAsyncThunk("slots/fetchSlots", async (startDate: string) => {
  const response = await fetch(`/api/slots?startDate=${startDate}`)
  if (!response.ok) {
    throw new Error("Failed to fetch slots")
  }
  const result = await response.json() as { data: Slots }
  return result.data
})

export const fetchMoreSlots = createAsyncThunk(
  "slots/fetchMoreSlots",
  async ({ direction, referenceDate }: { direction: "next" | "prev"; referenceDate: string }) => {
    const response = await fetch(`/api/slots?startDate=${referenceDate}&direction=${direction}`)
    if (!response.ok) {
      throw new Error("Failed to fetch more slots")
    }
    const result = await response.json() as { data: Slots }
    return { data: result.data, direction }
  },
)

const slotsSlice = createSlice({
  name: "slots",
  initialState,
  reducers: {
    setCurrentMonthIndex: (state, action: PayloadAction<number>) => {
      state.currentMonthIndex = action.payload
    },
    navigateMonth: (state, action: PayloadAction<"prev" | "next">) => {
      if (action.payload === "prev" && state.currentMonthIndex > 0) {
        state.currentMonthIndex -= 1
      } else if (action.payload === "next" && state.currentMonthIndex < state.months.length - 1) {
        state.currentMonthIndex += 1
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSlots.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchSlots.fulfilled, (state, action) => {
        state.loading = false
        state.rawSlots = action.payload
        state.months = convertSlotsToMonths(action.payload)
        state.currentMonthIndex = 0
        state.hasMoreNext = true // Assume there's more data initially
        state.hasMorePrev = false
      })
      .addCase(fetchSlots.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || "Failed to fetch slots"
      })
      .addCase(fetchMoreSlots.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchMoreSlots.fulfilled, (state, action) => {
        state.loading = false
        const { data, direction } = action.payload

        // Merge new slots with existing ones
        const mergedSlots = { ...state.rawSlots, ...data }
        state.rawSlots = mergedSlots

        // Convert merged slots to months
        const newMonths = convertSlotsToMonths(mergedSlots)

        if (direction === "next") {
          state.months = newMonths
          // Check if we got new data (if no new dates, assume no more data)
          const hasNewData = Object.keys(data).length > 0
          state.hasMoreNext = hasNewData
        } else {
          state.months = newMonths
          // Adjust current month index to maintain position
          const oldCurrentMonth = state.months[state.currentMonthIndex]
          const newCurrentIndex = newMonths.findIndex(
            (month) => month.month === oldCurrentMonth?.month && month.year === oldCurrentMonth?.year,
          )
          state.currentMonthIndex = newCurrentIndex >= 0 ? newCurrentIndex : 0

          const hasNewData = Object.keys(data).length > 0
          state.hasMorePrev = hasNewData
        }
      })
      .addCase(fetchMoreSlots.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || "Failed to fetch more slots"
      })
  },
})

export const { setCurrentMonthIndex, navigateMonth } = slotsSlice.actions
export default slotsSlice.reducer
