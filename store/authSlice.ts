import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { authClient } from '@/lib/auth-client';

/**
 * Interface for user object in authentication state.
 * @property id - User ID
 * @property email - User email
 * @property name - User name
 * @property image - User avatar image URL or null
 * @property emailVerified - Whether the user's email is verified
 * @property createdAt - ISO string of user creation date
 * @property updatedAt - ISO string of user update date
 */
export interface AuthUser {
    id: string;
    email: string;
    name: string;
    image: string | null;
    emailVerified: boolean;
    createdAt: string;
    updatedAt: string;
}

/**
 * Interface for authentication state.
 */
export interface AuthState {
    user: null | AuthUser;
    loading: boolean;
    error: string | null;
}

/**
 * Initial state for authentication.
 */
const initialState: AuthState = {
    user: null,
    loading: false,
    error: null,
};

/**
 * Normalizes a user object for Redux state.
 * Ensures all required fields are present and serializable.
 * Converts date fields to ISO strings.
 *
 * @param user - The user object to normalize
 * @returns The normalized AuthUser object
 */
export function normalizeUserForRedux(user: (Partial<Omit<AuthUser, 'createdAt' | 'updatedAt'>> & { createdAt?: unknown; updatedAt?: unknown }) | undefined | null): AuthUser | null {
    if (!user) return null;
    const normalizeDate = (value: unknown): string => {
        if (typeof value === 'string') return value;
        if (value instanceof Date) return value.toISOString();
        return '';
    };
    return {
        id: user.id ?? '',
        email: user.email ?? '',
        name: user.name ?? '',
        image: user.image ?? null,
        emailVerified: user.emailVerified ?? false,
        createdAt: normalizeDate(user.createdAt),
        updatedAt: normalizeDate(user.updatedAt),
    };
}

/**
 * Async thunk for logging in a user using Better Auth.
 * Ensures all date fields in user object are serialized as strings.
 *
 * @param payload - Object containing email and password
 * @returns The user session data on success
 */
export const loginUser = createAsyncThunk<
    { user: AuthUser } | undefined,
    { email: string; password: string },
    { rejectValue: string }
>(
    'auth/loginUser',
    async (payload, { rejectWithValue }) => {
        try {
            const { data, error } = await authClient.signIn.email({
                email: payload.email,
                password: payload.password,
            });
            if (error) {
                return rejectWithValue(error.message || 'Login failed');
            }
            return data && data.user ? { user: normalizeUserForRedux(data.user)! } : undefined;
        } catch (err) {
            return rejectWithValue((err as Error).message || 'Login failed');
        }
    }
);

/**
 * Async thunk for signing up a user using Better Auth.
 * Ensures all date fields in user object are serialized as strings.
 *
 * @param payload - Object containing email and password
 * @returns The user session data on success
 */
export const signUpUser = createAsyncThunk<
    { user: AuthUser } | undefined,
    { email: string; password: string },
    { rejectValue: string }
>(
    'auth/signUpUser',
    async (payload, { rejectWithValue }) => {
        try {
            const name = payload.email.split('@')[0];
            const { data, error } = await authClient.signUp.email({
                email: payload.email,
                password: payload.password,
                name,
            });
            if (error) {
                return rejectWithValue(error.message || 'Sign up failed');
            }
            return data && data.user ? { user: normalizeUserForRedux(data.user)! } : undefined;
        } catch (err) {
            return rejectWithValue((err as Error).message || 'Sign up failed');
        }
    }
);

/**
 * Async thunk to fetch the current authenticated user session on the client.
 * Uses authClient.getSession() to check for an existing session and hydrates Redux.
 *
 * @returns The user session data on success
 */
export const fetchCurrentUser = createAsyncThunk<
    { user: AuthUser } | undefined,
    void,
    { rejectValue: string }
>(
    'auth/fetchCurrentUser',
    async (_, { rejectWithValue }) => {
        try {
            const { data, error } = await authClient.getSession();
            if (error) {
                return rejectWithValue(error.message || 'Session fetch failed');
            }
            return data && data.user ? { user: normalizeUserForRedux(data.user)! } : undefined;
        } catch (err) {
            return rejectWithValue((err as Error).message || 'Session fetch failed');
        }
    }
);

/**
 * Redux slice for authentication (login & sign up).
 */
export const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        /**
         * Initiates login or sign up process.
         */
        authStart(state) {
            state.loading = true;
            state.error = null;
        },
        /**
         * Handles successful authentication.
         * @param action - Payload with user object
         */
        authSuccess(state, action: PayloadAction<AuthUser>) {
            state.user = action.payload;
            state.loading = false;
            state.error = null;
        },
        /**
         * Handles authentication failure.
         * @param action - Payload with error message
         */
        authFailure(state, action: PayloadAction<string>) {
            state.loading = false;
            state.error = action.payload;
        },
        /**
         * Logs out the user.
         */
        logout(state) {
            state.user = null;
            state.loading = false;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(loginUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(loginUser.fulfilled, (state, action: PayloadAction<{ user: AuthUser } | undefined>) => {
                state.user = action.payload?.user ?? null;
                state.loading = false;
                state.error = null;
            })
            .addCase(loginUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || 'Login failed';
            })
            // Handle sign up
            .addCase(signUpUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(signUpUser.fulfilled, (state, action: PayloadAction<{ user: AuthUser } | undefined>) => {
                state.user = action.payload?.user ?? null;
                state.loading = false;
                state.error = null;
            })
            .addCase(signUpUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || 'Sign up failed';
            })
            // Handle fetch current user
            .addCase(fetchCurrentUser.fulfilled, (state, action: PayloadAction<{ user: AuthUser } | undefined>) => {
                state.user = action.payload?.user ?? null;
            });
    },
});

export const { authStart, authSuccess, authFailure, logout } = authSlice.actions;

/**
 * Selector to get the authenticated user from the Redux store.
 * @param state - The root Redux state
 * @returns The authenticated user or null if not logged in
 */
export const getAuthUser = (state: { auth: AuthState }): AuthUser | null => state.auth.user;

export default authSlice.reducer; 