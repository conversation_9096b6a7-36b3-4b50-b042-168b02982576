import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { fetchGroups } from "./groupSlice";

/**
 * Service type for public browsing (from features)
 */
export type Service = {
  id: string;
  name: string;
  externalName: string;
  duration: number;
  price: string;
  gross: number;
  groupNames?: string[];
  productGroup?: string;
};

/**
 * Service group type for public browsing
 */
export type ServiceGroup = {
  id: string | undefined;
  name: string;
  services: Service[];
};

/**
 * Consolidated state for Services slice - combines admin and public functionality
 */
export interface ServicesState {
    // Admin functionality (from original serviceSlice)
    adminServices: IStoreService[];
    adminLoading: boolean;
    adminError: string | null;
    editServiceStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    editServiceError: string | null;
    uploadServiceImageStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    uploadServiceImageError: string | null;
    deleteServiceStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    deleteServiceError: string | null;
    alterServicePositionStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    alterServicePositionError: string | null;
    
    // Public browsing functionality (from features servicesSlice)
    groups: ServiceGroup[];
    allServices: Service[];
    publicLoading: boolean;
    publicError: string | null;
    selectedGroupId: string;
}

const initialState: ServicesState = {
    // Admin state
    adminServices: [],
    adminLoading: false,
    adminError: null,
    editServiceStatus: 'idle',
    editServiceError: null,
    uploadServiceImageStatus: 'idle',
    uploadServiceImageError: null,
    deleteServiceStatus: 'idle',
    deleteServiceError: null,
    alterServicePositionStatus: 'idle',
    alterServicePositionError: null,
    
    // Public browsing state
    groups: [],
    allServices: [],
    publicLoading: false,
    publicError: null,
    selectedGroupId: "alle",
};

/**
 * Async thunk to fetch all services for admin.
 */
export const fetchAdminServices = createAsyncThunk<IStoreService[], void, { rejectValue: string }>(
    "services/fetchAdminServices",
    async (_, { rejectWithValue }) => {
        try {
            const res = await fetch("/api/service");
            if (!res.ok) throw new Error("Failed to fetch services");
            const data = await res.json() as IStoreService[];
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to fetch services for public browsing.
 */
export const fetchPublicServices = createAsyncThunk("services/fetchPublicServices", async () => {
  const response = await fetch("/api/services");
  if (!response.ok) {
    throw new Error("Failed to fetch services");
  }
  const data = await response.json();

  // Create "Alle" group with all services
  const allServices = data.groups.flatMap((group: ServiceGroup) => group.services);
  const alleGroup = {
    id: "alle",
    name: "Alle",
    services: allServices,
  };

  return {
    groups: [alleGroup, ...data.groups],
    allServices,
  };
});

/**
 * Async thunk to edit a service.
 */
export const editService = createAsyncThunk<
    void,
    {
        id: string;
        displayName?: string;
        displayDescription?: string;
        duration?: number;
        price?: number;
        image?: string;
        hidden?: boolean;
    },
    { rejectValue: string }
>(
    "services/editService",
    async (payload, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });
            if (!res.ok) throw new Error("Failed to edit service");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to upload a service image.
 */
export const uploadServiceImage = createAsyncThunk<
    { url: string; id: string },
    { file: File; id: string },
    { rejectValue: string }
>(
    "services/uploadServiceImage",
    async ({ file, id }, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("id", id);
            const res = await fetch("/api/service/image", {
                method: "POST",
                body: formData,
            });
            if (!res.ok) throw new Error("Failed to upload image");
            const data: any = await res.json();
            return { url: data.url, id: data.id };
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to delete (hide) a service.
 */
export const deleteService = createAsyncThunk<
    void,
    { id: string },
    { rejectValue: string }
>(
    "services/deleteService",
    async ({ id }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "DELETE",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id }),
            });
            if (!res.ok) throw new Error("Failed to delete service");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to alter the position of a service.
 */
export const alterServicePosition = createAsyncThunk<
    void,
    { displayOrder: number; id: string },
    { rejectValue: string }
>(
    "services/alterServicePosition",
    async ({ displayOrder, id }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ displayOrder, id }),
            });
            if (!res.ok) throw new Error("Failed to alter service position");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

export const servicesSlice = createSlice({
    name: "services",
    initialState,
    reducers: {
        // Public browsing reducers
        setSelectedGroup: (state, action: PayloadAction<string>) => {
            state.selectedGroupId = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            // Admin services
            .addCase(fetchAdminServices.pending, (state) => {
                state.adminLoading = true;
                state.adminError = null;
            })
            .addCase(fetchAdminServices.fulfilled, (state, action: PayloadAction<IStoreService[]>) => {
                state.adminServices = action.payload;
                state.adminLoading = false;
            })
            .addCase(fetchAdminServices.rejected, (state, action) => {
                state.adminLoading = false;
                state.adminError = action.payload || "Failed to fetch services";
            })
            // Public services
            .addCase(fetchPublicServices.pending, (state) => {
                state.publicLoading = true;
                state.publicError = null;
            })
            .addCase(fetchPublicServices.fulfilled, (state, action) => {
                state.publicLoading = false;
                state.groups = action.payload.groups;
                state.allServices = action.payload.allServices;
            })
            .addCase(fetchPublicServices.rejected, (state, action) => {
                state.publicLoading = false;
                state.publicError = action.error.message || "Failed to fetch services";
            })
            // editService
            .addCase(editService.pending, (state) => {
                state.editServiceStatus = 'pending';
                state.editServiceError = null;
            })
            .addCase(editService.fulfilled, (state) => {
                state.editServiceStatus = 'succeeded';
            })
            .addCase(editService.rejected, (state, action) => {
                state.editServiceStatus = 'failed';
                state.editServiceError = action.payload || 'Failed to edit service';
            })
            // uploadServiceImage
            .addCase(uploadServiceImage.pending, (state) => {
                state.uploadServiceImageStatus = 'pending';
                state.uploadServiceImageError = null;
            })
            .addCase(uploadServiceImage.fulfilled, (state) => {
                state.uploadServiceImageStatus = 'succeeded';
            })
            .addCase(uploadServiceImage.rejected, (state, action) => {
                state.uploadServiceImageStatus = 'failed';
                state.uploadServiceImageError = action.payload || 'Failed to upload image';
            })
            // deleteService
            .addCase(deleteService.pending, (state) => {
                state.deleteServiceStatus = 'pending';
                state.deleteServiceError = null;
            })
            .addCase(deleteService.fulfilled, (state) => {
                state.deleteServiceStatus = 'succeeded';
            })
            .addCase(deleteService.rejected, (state, action) => {
                state.deleteServiceStatus = 'failed';
                state.deleteServiceError = action.payload || 'Failed to delete service';
            })
            // alterServicePosition
            .addCase(alterServicePosition.pending, (state) => {
                state.alterServicePositionStatus = 'pending';
                state.alterServicePositionError = null;
            })
            .addCase(alterServicePosition.fulfilled, (state) => {
                state.alterServicePositionStatus = 'succeeded';
            })
            .addCase(alterServicePosition.rejected, (state, action) => {
                state.alterServicePositionStatus = 'failed';
                state.alterServicePositionError = action.payload || 'Failed to alter service position';
            });
    },
});

export const { setSelectedGroup } = servicesSlice.actions;

export default servicesSlice.reducer;
