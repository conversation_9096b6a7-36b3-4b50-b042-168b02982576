import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Appointment, Patient, AppointmentsResponse } from "@/types/booking";

/**
 * State for the appointment management slice (admin/user appointment viewing)
 */
export interface AppointmentManagementState {
  appointments: Appointment[];
  patient: Patient | null;
  loading: boolean;
  error: string | null;
  cancelLoading: boolean;
  cancelError: string | null;
  rescheduleLoading: boolean;
  rescheduleError: string | null;
}

const initialState: AppointmentManagementState = {
  appointments: [],
  patient: null,
  loading: false,
  error: null,
  cancelLoading: false,
  cancelError: null,
  rescheduleLoading: false,
  rescheduleError: null,
};

/**
 * Async thunk to fetch user appointments
 */
export const fetchAppointments = createAsyncThunk<
  AppointmentsResponse,
  { email?: string; phone?: string },
  { rejectValue: string }
>(
  "appointmentManagement/fetchAppointments",
  async ({ email, phone }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      if (email) params.append('email', email);
      if (phone) params.append('phone', phone);

      const response = await fetch(`/api/booking/appointments?${params}`);
      if (!response.ok) {
        if (response.status === 404) {
          return { appointments: [], patient: null } as unknown as AppointmentsResponse;
        }
        throw new Error("Failed to fetch appointments");
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch appointments");
    }
  }
);

/**
 * Async thunk to cancel an appointment
 */
export const cancelAppointment = createAsyncThunk<
  void,
  { appointmentId: string },
  { rejectValue: string }
>(
  "appointmentManagement/cancelAppointment",
  async ({ appointmentId }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error("Failed to cancel appointment");
      }
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to cancel appointment");
    }
  }
);

/**
 * Async thunk to reschedule an appointment
 */
export const rescheduleAppointment = createAsyncThunk<
  void,
  { appointmentId: string; newDateTime: string },
  { rejectValue: string }
>(
  "appointmentManagement/rescheduleAppointment",
  async ({ appointmentId, newDateTime }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}/reschedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ newDateTime }),
      });

      if (!response.ok) {
        throw new Error("Failed to reschedule appointment");
      }
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to reschedule appointment");
    }
  }
);

export const appointmentManagementSlice = createSlice({
  name: "appointmentManagement",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.cancelError = null;
      state.rescheduleError = null;
    },
    clearAppointments: (state) => {
      state.appointments = [];
      state.patient = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch appointments
      .addCase(fetchAppointments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAppointments.fulfilled, (state, action: PayloadAction<AppointmentsResponse>) => {
        state.loading = false;
        state.appointments = action.payload.appointments;
        state.patient = action.payload.patient;
      })
      .addCase(fetchAppointments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch appointments";
      })
      // Cancel appointment
      .addCase(cancelAppointment.pending, (state) => {
        state.cancelLoading = true;
        state.cancelError = null;
      })
      .addCase(cancelAppointment.fulfilled, (state) => {
        state.cancelLoading = false;
      })
      .addCase(cancelAppointment.rejected, (state, action) => {
        state.cancelLoading = false;
        state.cancelError = action.payload || "Failed to cancel appointment";
      })
      // Reschedule appointment
      .addCase(rescheduleAppointment.pending, (state) => {
        state.rescheduleLoading = true;
        state.rescheduleError = null;
      })
      .addCase(rescheduleAppointment.fulfilled, (state) => {
        state.rescheduleLoading = false;
      })
      .addCase(rescheduleAppointment.rejected, (state, action) => {
        state.rescheduleLoading = false;
        state.rescheduleError = action.payload || "Failed to reschedule appointment";
      });
  },
});

export const { clearError, clearAppointments } = appointmentManagementSlice.actions;

export default appointmentManagementSlice.reducer;
