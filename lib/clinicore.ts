import qs from 'qs';
import { getEnv } from '@/utils/ctx';
import { CCService, CCProductGroup, CCSlot, CCPatient, CCAppointment } from '@/types/booking';

/**
 * CliniCore API integration service
 * Migrated and modernized from the old system
 */

interface CCRequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: Record<string, unknown>;
  params?: Record<string, string | number | boolean | null>;
}

interface CCApiResponse {
  error?: {
    message?: string;
  };
  [key: string]: unknown;
}

/**
 * Make a request to the CliniCore API
 */
export async function ccRequest(
  url: string,
  options: string | CCRequestOptions
): Promise<CCApiResponse> {
  try {
    const env = await getEnv();

    if (!env.CC_API_TOKEN) {
      throw new Error('CC Token not found in the environment');
    }

    const requestOptions: {
      method: string;
      body?: string;
      headers: Record<string, string>;
    } = {
      method: 'GET',
      headers: {
        Authorization: 'Bearer ' + env.CC_API_TOKEN,
        'Accept-Encoding': 'application/json',
        'Content-Type': 'application/json',
      },
    };

    let reqUrl = env.CC_API_URL + url;

    if (typeof options === 'string') {
      requestOptions.method = options;
    } else {
      requestOptions.method = options.method;
      if (options.body) {
        requestOptions.body = JSON.stringify(options.body);
      }
      if (options.headers) {
        requestOptions.headers = {
          ...requestOptions.headers,
          ...options.headers,
        };
      }
      if (options.params) {
        reqUrl += '?' + qs.stringify(options.params);
      }
    }

    const response = await fetch(reqUrl, requestOptions);
    const data = await response.json() as CCApiResponse;

    if (data && data.error) {
      throw new Error(data.error.message || 'CliniCore API error');
    }

    return data;
  } catch (error) {
    console.error('CliniCore API Error:', error);
    throw error;
  }
}

interface GroupedService {
  name: string;
  id?: string;
  services: (CCService & {
    duration: number;
    gross: number;
    externalName: string;
  })[];
}

/**
 * Fetch services from CliniCore and group them
 */
export async function fetchServicesFromCC(): Promise<{
  groupedServices: GroupedService[];
  allServices: CCService[];
}> {
  try {
    const env = await getEnv();

    const requestOptions = {
      method: "GET" as const,
      headers: {
        Authorization: "Bearer " + env.CC_PUBLIC_TOKEN,
        "Accept-Encoding": "application/json",
        "Content-Type": "application/json",
      },
    };

    // Fetch product groups and online services in parallel
    const [productGroupsRes, onlineServicesRes] = await Promise.all([
      ccRequest("/productGroups", "GET"),
      fetch("https://scheduler.clinicore.eu/api/scheduler/services", requestOptions).then((res) => res.json()),
    ]);

    const productGroups: CCProductGroup[] = (productGroupsRes as { productGroups?: CCProductGroup[] })?.productGroups ?? [];
    const onlineServices: CCService[] = (onlineServicesRes as { services?: CCService[] })?.services ?? [];

    // Group online services by group name
    const groupNameMap: Record<string, CCService[]> = {};
    onlineServices.forEach((service) => {
      if (service.groupNames) {
        service.groupNames.forEach((gName: string) => {
          if (!groupNameMap[gName]) groupNameMap[gName] = [];
          groupNameMap[gName].push(service);
        });
      }
    });

    // Prepare grouped data
    const groupedServices = Object.keys(groupNameMap)
      .filter((key) => key.trim().length)
      .map((key) => {
        const group = productGroups.find(pg => pg.name === key);
        return {
          name: key,
          id: group ? group.id : undefined,
          services: groupNameMap[key].map((service) => ({
            ...service,
            duration: service.duration / 60, // Convert to minutes
            gross: parseFloat(service.price),
            externalName: service.name,
          })),
        };
      });

    // Fetch all services from ccRequest
    const allServicesRes = await ccRequest("/services", "GET");
    const allServices: CCService[] = (allServicesRes as { services?: CCService[] })?.services ?? [];

    return {
      groupedServices,
      allServices: allServices.filter((s) =>
        onlineServices.some(os => os.id === s.id)
      ),
    };
  } catch (error) {
    console.error('Error fetching services from CliniCore:', error);
    throw error;
  }
}

/**
 * Fetch available time slots from CliniCore
 */
export async function fetchSlotsFromCC(params: {
  service?: number;
  duration?: number;
  user?: number;
  start?: string;
  end?: string;
}): Promise<CCSlot[]> {
  try {
    const now = new Date();
    const query: any = {
      from: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
      to: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
    };

    if (params.duration) {
      query.duration = params.duration * 60; // Convert minutes to seconds
    } else {
      query.duration = 1800; // Default 30 minutes
    }

    if (params.service) query.service = params.service;
    if (params.user) query.user = params.user;
    if (params.start) {
      const startDate = new Date(params.start);
      query.from = startDate.toISOString();
      query.to = new Date(startDate.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString();
    }
    if (params.end) query.to = new Date(params.end).toISOString();

    const result = await ccRequest('/slots', {
      method: 'GET',
      params: query,
    });

    return (result as { slots?: CCSlot[] })?.slots ?? [];
  } catch (error) {
    console.error('Error fetching slots from CliniCore:', error);
    throw error;
  }
}

/**
 * Search for a patient in CliniCore
 */
export async function searchPatientInCC(contact: { email?: string | null; phone?: string | null }): Promise<CCPatient | null> {
  try {
    if (!contact.phone && !contact.email) {
      throw new Error("Invalid data: Email and phone is missing");
    }

    let patient: CCPatient | null = null;

    if (contact.email) {
      const result = await ccRequest(`/patients?search=${contact.email}`, 'GET');
      const patientsResult = result as { patients?: CCPatient[] };
      if (patientsResult.patients && patientsResult.patients.length > 0) {
        patient = patientsResult.patients[0];
      }
    }

    if (!patient && contact.phone) {
      const result = await ccRequest(`/patients?search=${contact.phone}`, 'GET');
      const patientsResult = result as { patients?: CCPatient[] };
      if (patientsResult.patients && patientsResult.patients.length > 0) {
        patient = patientsResult.patients[0];
      }
    }

    return patient;
  } catch (error) {
    console.error('Error searching patient in CliniCore:', error);
    return null;
  }
}

interface PatientData {
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  birthDay?: string;
  message?: string;
  [key: string]: unknown;
}

/**
 * Create or update a patient in CliniCore
 */
export async function createOrUpdatePatientInCC(patientData: PatientData): Promise<CCPatient> {
  try {
    // First try to find existing patient
    const existingPatient = await searchPatientInCC({
      email: patientData.email,
      phone: patientData.phone,
    });

    if (existingPatient) {
      // Update existing patient
      return await updatePatientInCC(existingPatient.id, patientData);
    } else {
      // Create new patient
      return await createPatientInCC(patientData);
    }
  } catch (error) {
    console.error('Error creating/updating patient in CliniCore:', error);
    throw error;
  }
}

interface CCPatientPayload {
  active: boolean;
  email?: string;
  phoneMobile?: string;
  firstName?: string;
  lastName?: string;
  dob?: string;
  customFields?: CustomFieldValue[];
}

/**
 * Create a new patient in CliniCore
 */
async function createPatientInCC(patientData: PatientData): Promise<CCPatient> {
  const { email, phone, firstName, lastName, birthDay } = patientData;

  if (!email && !phone) {
    throw new Error('Email and phone number is missing.');
  }

  const ccPayload: CCPatientPayload = {
    active: true,
  };

  if (email) ccPayload.email = email;
  if (phone) ccPayload.phoneMobile = formatPhoneNumber(phone);
  if (firstName) ccPayload.firstName = firstName;
  if (lastName) ccPayload.lastName = lastName;
  if (birthDay) ccPayload.dob = new Date(birthDay).toISOString();

  // Map custom fields
  ccPayload.customFields = await mapCCCustomFields(patientData);

  const result = await ccRequest('/patients', {
    method: 'POST',
    body: { patient: ccPayload },
  });

  return (result as { patient?: CCPatient })?.patient as CCPatient;
}

/**
 * Update an existing patient in CliniCore
 */
async function updatePatientInCC(patientId: number, patientData: PatientData): Promise<CCPatient> {
  const { email, phone, firstName, lastName, birthDay } = patientData;

  const ccPayload: CCPatientPayload = {
    active: true,
  };

  if (email) ccPayload.email = email;
  if (phone) ccPayload.phoneMobile = formatPhoneNumber(phone);
  if (firstName) ccPayload.firstName = firstName;
  if (lastName) ccPayload.lastName = lastName;
  if (birthDay) ccPayload.dob = new Date(birthDay).toISOString();

  ccPayload.customFields = await mapCCCustomFields(patientData);

  const result = await ccRequest(`/patients/${patientId}`, {
    method: 'PUT',
    body: { patient: ccPayload },
  });

  return (result as { patient?: CCPatient })?.patient as CCPatient;
}

/**
 * Create an appointment in CliniCore
 */
export async function createAppointmentInCC(appointmentData: {
  slot: Date;
  duration: number;
  patient: CCPatient;
  services: number[];
  user?: number;
  message?: string;
}): Promise<CCAppointment> {
  try {
    const { slot, duration, patient, services, user, message } = appointmentData;

    const ccAppointmentPayload = {
      slot: false,
      startsAt: slot.toISOString(),
      endsAt: new Date(slot.getTime() + duration * 1000).toISOString(),
      patients: [patient.id],
      people: user ? [user] : [],
      services: services,
      description: message,
    };

    const result = await ccRequest('/appointments', {
      method: 'POST',
      body: { appointment: ccAppointmentPayload },
    });

    return (result as { appointment?: CCAppointment })?.appointment as CCAppointment;
  } catch (error) {
    console.error('Error creating appointment in CliniCore:', error);
    throw error;
  }
}

interface CustomFieldValue {
  field: CCCustomField;
  values: Array<{ value?: unknown; id?: string }>;
  patient: null;
}

interface CCCustomField {
  id: string;
  name: string;
  label: string;
  allowedValues: Array<{ id: string; value: unknown }>;
}

/**
 * Map custom fields for CliniCore
 */
async function mapCCCustomFields(body: PatientData): Promise<CustomFieldValue[]> {
  try {
    const customFieldsResult = await ccRequest('/customFields', 'GET');
    const customFields = (customFieldsResult as { customFields?: CCCustomField[] })?.customFields;
    const { email = null, phone = null, message = null } = body;
    const matchedProperties: CustomFieldValue[] = [];

    if (customFields && customFields.length > 0) {
      const apCFNameValue: Record<string, unknown> = {};
      if (email) apCFNameValue['email'] = email;
      if (phone) apCFNameValue['phoneMobile'] = formatPhoneNumber(phone);
      if (phone) apCFNameValue['phone-mobile'] = formatPhoneNumber(phone);
      if (message) apCFNameValue['note'] = message;

      if (body.tos) apCFNameValue['newsletter-wanted'] = body.tos;
      if (body.birthDay) apCFNameValue['Geburtsdatum'] = body.birthDay;

      Object.keys(apCFNameValue).forEach((cf) => {
        const match = customFields.find(
          (ccf) => ccf.name === cf || ccf.label === cf
        );
        if (match) {
          const value: CustomFieldValue = {
            field: match,
            values: [{ value: apCFNameValue[cf] }],
            patient: null,
          };
          if (match.allowedValues.length > 0) {
            const allowedValue = match.allowedValues.find((v) => v.value === apCFNameValue[cf]);
            if (allowedValue) {
              value.values = [{ id: allowedValue.id }];
            }
          }
          matchedProperties.push(value);
        }
      });
    }
    return matchedProperties;
  } catch (error) {
    console.error('Error mapping custom fields:', error);
    return [];
  }
}

/**
 * Format phone number for CliniCore
 */
function formatPhoneNumber(phoneNumber: string): string {
  const cleanedNumber = phoneNumber.replace(/\D/g, '');
  let countryCode = cleanedNumber.startsWith('49') ? '49' : '';
  const restOfNumber = cleanedNumber.slice(countryCode.length);
  const formattedNumber = `+${countryCode}${restOfNumber}`;
  return formattedNumber;
}
